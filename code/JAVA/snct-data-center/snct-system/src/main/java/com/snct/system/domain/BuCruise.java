package com.snct.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * 航次对象 bu_cruise
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public class BuCruise extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 航次ID */
    private Long cruiseId;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    private String deptName;

    /** 船只sn号 */
    @Excel(name = "船只sn号")
    private String sn;

    private String shipName;

    /** 船长 */
    @Excel(name = "船长")
    private String captain;

    /** 航次编号 */
    @Excel(name = "航次编号")
    private String code;

    /** 航次开始时间 */
    @Excel(name = "航次开始时间")
    private String startTime;

    /** 航次结束时间 */
    @Excel(name = "航次结束时间")
    private String finishTime;

    /** 总天数 */
    @Excel(name = "总天数")
    private Long totalDays;

    /** 历史里程 */
    @Excel(name = "历史里程")
    private Long historyMileage;

    /** 起始地 */
    @Excel(name = "起始地")
    private String startPort;

    /** 目的地 */
    @Excel(name = "目的地")
    private String endPort;

    /** 目标海域 */
    @Excel(name = "目标海域")
    private String seaArea;

    /** 属性json */
    @Excel(name = "属性json")
    private String moduleJson;

    public void setCruiseId(Long cruiseId) 
    {
        this.cruiseId = cruiseId;
    }

    public Long getCruiseId() 
    {
        return cruiseId;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public void setSn(String sn) 
    {
        this.sn = sn;
    }

    public String getSn()
    {
        return sn;
    }

    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public void setShipName(String shipName)
    {
        this.shipName = shipName;
    }

    public String getShipName()
    {
        return shipName;
    }

    public void setCaptain(String captain) 
    {
        this.captain = captain;
    }

    public String getCaptain() 
    {
        return captain;
    }

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setStartTime(String startTime)
    {
        this.startTime = startTime;
    }

    public String getStartTime()
    {
        return startTime;
    }

    public void setFinishTime(String finishTime)
    {
        this.finishTime = finishTime;
    }

    public String getFinishTime()
    {
        return finishTime;
    }

    public void setTotalDays(Long totalDays) 
    {
        this.totalDays = totalDays;
    }

    public Long getTotalDays() 
    {
        return totalDays;
    }

    public void setHistoryMileage(Long historyMileage) 
    {
        this.historyMileage = historyMileage;
    }

    public Long getHistoryMileage() 
    {
        return historyMileage;
    }

    public void setStartPort(String startPort) 
    {
        this.startPort = startPort;
    }

    public String getStartPort() 
    {
        return startPort;
    }

    public void setEndPort(String endPort) 
    {
        this.endPort = endPort;
    }

    public String getEndPort() 
    {
        return endPort;
    }

    public void setSeaArea(String seaArea) 
    {
        this.seaArea = seaArea;
    }

    public String getSeaArea() 
    {
        return seaArea;
    }

    public void setModuleJson(String moduleJson) 
    {
        this.moduleJson = moduleJson;
    }

    public String getModuleJson() 
    {
        return moduleJson;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("cruiseId", getCruiseId())
            .append("deptId", getDeptId())
            .append("sn", getSn())
            .append("captain", getCaptain())
            .append("code", getCode())
            .append("startTime", getStartTime())
            .append("finishTime", getFinishTime())
            .append("totalDays", getTotalDays())
            .append("historyMileage", getHistoryMileage())
            .append("startPort", getStartPort())
            .append("endPort", getEndPort())
            .append("seaArea", getSeaArea())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("moduleJson", getModuleJson())
            .toString();
    }
}
