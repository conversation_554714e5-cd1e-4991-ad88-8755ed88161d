package com.snct.system.service;

import java.util.List;
import com.snct.system.domain.BuDeviceConfig;

/**
 * 设备配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
public interface IBuDeviceConfigService 
{
    /**
     * 查询设备配置
     * 
     * @param id 设备配置主键
     * @return 设备配置
     */
    public BuDeviceConfig selectBuDeviceConfigById(Long id);

    /**
     *通过设备Id 与  key   查询具体设备的 配置
     * @param buDeviceConfig
     * @return 设备配置
     */
    public BuDeviceConfig selectBydeviceIdAndKey(BuDeviceConfig buDeviceConfig);

    /**
     * 查询设备配置列表
     * 
     * @param buDeviceConfig 设备配置
     * @return 设备配置集合
     */
    public List<BuDeviceConfig> selectBuDeviceConfigList(BuDeviceConfig buDeviceConfig);

    /**
     * 新增设备配置
     * 
     * @param buDeviceConfig 设备配置
     * @return 结果
     */
    public int insertBuDeviceConfig(BuDeviceConfig buDeviceConfig);

    /**
     * 修改设备配置
     * 
     * @param buDeviceConfig 设备配置
     * @return 结果
     */
    public int updateBuDeviceConfig(BuDeviceConfig buDeviceConfig);

    /**
     * 批量删除设备配置
     * 
     * @param ids 需要删除的设备配置主键集合
     * @return 结果
     */
    public int deleteBuDeviceConfigByIds(Long[] ids);

    /**
     * 删除设备配置信息
     * 
     * @param id 设备配置主键
     * @return 结果
     */
    public int deleteBuDeviceConfigById(Long id);
}
